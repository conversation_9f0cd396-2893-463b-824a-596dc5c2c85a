package com.excelfore.esync.brp

import com.excelfore.esync.BuildConfig

interface Constants {
    companion object {
        const val TAG: String = "eSync"

        const val G2E_CHECK_FOR_UPDATE: String = "ACTION_CHECK_FOR_UPDATE"
        const val G2E_START_DOWNLOAD: String = "ACTION_START_DOWNLOAD"
        const val G2E_INSTALL_PROGRESS: String = "ACTION_INSTALL_PROGRESS"
        const val G2E_INSTALL_POST_REBOOT: String = "ACTION_INSTALL_POST_REBOOT"
        const val G2E_ABORT_UPDATE_ACK: String = "ACTION_ABORT_UPDATE_ACK"

        // https://docs.google.com/spreadsheets/d/1fDrAEhOwi0B13JYGd2f0NEjgrZzyo8VxiQmBxE6b0jA/edit?gid=0#gid=0
        // Sheet "Intents & Actions", but those names are wrong.
        // https://docs.google.com/spreadsheets/d/1fDrAEhOwi0B13JYGd2f0NEjgrZzyo8VxiQmBxE6b0jA/edit?gid=0#gid=0
        // Action item 96 to define those correctly.
        const val GUSS_INTENT_PREFIX: String = "com.brp.guss"
        const val GUSS_PKG_NAME = "com.android.car.settings"
        const val E2G_UPDATE_AVAILABLE_RESULT: String =
            "$GUSS_INTENT_PREFIX.ACTION_UPDATE_AVAILABLE_RESULT"
        const val E2G_DOWNLOAD_PROGRESS: String = "$GUSS_INTENT_PREFIX.ACTION_DOWNLOAD_PROGRESS"
        const val E2G_INSTALL_POST_REBOOT_ACK: String =
            "$GUSS_INTENT_PREFIX.ACTION_INSTALL_POST_REBOOT_ACK"

        const val NOTIFICATION_CHANNEL_ID: String = "com.excelfore.esync.agent"
        const val FOREGROUND_SERVICE_NOTIFICATION: Int = 1228

        const val GUSS_LOSS_TIMEOUT_PROP: String = "debug.excelfore.esync.brp.guss_time_out"

        const val SERIAL_NO_FA = "ecuSerial"
        const val USED_SIGNING_CERT = "sign.cert"

        const val OTA_CONTAINER_ROOT = "OTA_Container"
        const val KSS_BIN = "kss_bins"
        const val OTA_JSON = "android-ota-full.json"
        const val OTA_JSON_PATH = "ota/$OTA_JSON"
        const val OTA_ZIP = "android-ota-full.zip"

        const val LARGE_FILE_BUFFER_SIZE = 8 * 1024 * 1024

        private val ALL_ACTION_SUFFIXES = listOf(
            G2E_CHECK_FOR_UPDATE,
            G2E_START_DOWNLOAD,
            G2E_INSTALL_PROGRESS,
            G2E_INSTALL_POST_REBOOT,
            G2E_ABORT_UPDATE_ACK
        )

        val MY_ACTIONS: List<String> = ALL_ACTION_SUFFIXES.map { "${BuildConfig.APPLICATION_ID}.$it" }

    }
}
